import crypto from 'crypto';
import { TestCase, ValidationResult } from './types';

/**
 * Compute a deterministic signature for a test case
 * Adapted from skatebench's computeTestSignature function
 */
export function computeTestSignature(test: TestCase, systemPrompt: string): string {
  const signature = `${systemPrompt}|||${test.prompt}|||${test.answers.join('|||')}`;
  return signature;
}

/**
 * Generate a short hash ID from a signature string
 * Creates a deterministic 8-character hash for test identification
 */
export function signatureHash(signature: string): string {
  return crypto.createHash('sha256').update(signature).digest('hex').substring(0, 8);
}

/**
 * Sanitize a string for safe use in filenames
 * Removes or replaces characters that are invalid in file paths
 */
export function safeFilename(str: string): string {
  return str
    .toLowerCase()
    .replace(/[^a-z0-9\-_]/g, '-')
    .replace(/-+/g, '-')
    .replace(/^-|-$/g, '');
}

/**
 * Validate an individual test case structure
 * Ensures required fields are present and properly formatted
 */
export function validateTestCase(testCase: any): ValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  if (!testCase || typeof testCase !== 'object') {
    errors.push('Test case must be an object');
    return { valid: false, errors, warnings };
  }

  // Validate prompt
  if (!testCase.prompt || typeof testCase.prompt !== 'string') {
    errors.push('Test case must have a non-empty prompt string');
  } else if (testCase.prompt.trim().length === 0) {
    errors.push('Test case prompt cannot be empty or whitespace only');
  }

  // Validate answers
  if (!Array.isArray(testCase.answers)) {
    errors.push('Test case must have an answers array');
  } else if (testCase.answers.length === 0) {
    errors.push('Test case must have at least one answer');
  } else {
    testCase.answers.forEach((answer: any, index: number) => {
      if (typeof answer !== 'string') {
        errors.push(`Answer at index ${index} must be a string`);
      } else if (answer.trim().length === 0) {
        warnings.push(`Answer at index ${index} is empty or whitespace only`);
      }
    });
  }

  // Validate negative_answers if present
  if (testCase.negative_answers !== undefined) {
    if (!Array.isArray(testCase.negative_answers)) {
      errors.push('negative_answers must be an array if provided');
    } else {
      testCase.negative_answers.forEach((answer: any, index: number) => {
        if (typeof answer !== 'string') {
          errors.push(`Negative answer at index ${index} must be a string`);
        }
      });
    }
  }

  return {
    valid: errors.length === 0,
    errors,
    warnings
  };
}

/**
 * Normalize answer arrays for consistent comparison
 * Trims whitespace and removes empty strings
 */
export function normalizeAnswers(answers: string[]): string[] {
  return answers
    .map(answer => answer.trim())
    .filter(answer => answer.length > 0);
}

/**
 * Validate a complete test suite structure
 * Checks all required fields and validates each test case
 */
export function validateTestSuite(suite: any): ValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  if (!suite || typeof suite !== 'object') {
    errors.push('Test suite must be an object');
    return { valid: false, errors, warnings };
  }

  // Validate required string fields
  const requiredStringFields = ['name', 'description', 'system_prompt'];
  for (const field of requiredStringFields) {
    if (!suite[field] || typeof suite[field] !== 'string') {
      errors.push(`Test suite must have a non-empty ${field} string`);
    } else if (suite[field].trim().length === 0) {
      errors.push(`Test suite ${field} cannot be empty or whitespace only`);
    }
  }

  // Validate tests array
  if (!Array.isArray(suite.tests)) {
    errors.push('Test suite must have a tests array');
  } else if (suite.tests.length === 0) {
    errors.push('Test suite must have at least one test case');
  } else {
    suite.tests.forEach((test: any, index: number) => {
      const testValidation = validateTestCase(test);
      testValidation.errors.forEach(error => {
        errors.push(`Test case ${index}: ${error}`);
      });
      testValidation.warnings.forEach(warning => {
        warnings.push(`Test case ${index}: ${warning}`);
      });
    });
  }

  return {
    valid: errors.length === 0,
    errors,
    warnings
  };
}

/**
 * Extract test suite ID from filename
 * Removes .json extension and sanitizes the result
 */
export function extractSuiteId(filename: string): string {
  const baseName = filename.replace(/\.json$/i, '');
  return safeFilename(baseName);
}
