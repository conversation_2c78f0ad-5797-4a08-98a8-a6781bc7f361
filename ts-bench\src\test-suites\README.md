# Test Suites System

A new test suite system that combines skatebench's simple JSON format with ts-bench's coding agent evaluation capabilities. This system allows you to create structured test cases for evaluating AI coding agents using a simple, declarative JSON format.

## Overview

The test suite system provides:
- **Simple JSON Format**: Easy-to-write test cases inspired by skatebench
- **TypeScript Support**: Full type safety and IntelliSense support
- **Deterministic IDs**: Consistent test identification across runs
- **Validation**: Built-in validation for test suite structure
- **Extensible**: Easy to add new test suites and categories

## JSON Schema

Each test suite is a JSON file with the following structure:

```json
{
  "name": "Test Suite Name",
  "description": "Description of what this test suite evaluates",
  "system_prompt": "Instructions and context for the AI agent",
  "tests": [
    {
      "prompt": "Task description for the AI agent",
      "answers": ["expected", "patterns", "in", "response"],
      "negative_answers": ["incorrect", "patterns"] // optional
    }
  ]
}
```

### Fields

- **name**: Human-readable name for the test suite
- **description**: Detailed description of the test suite's purpose
- **system_prompt**: Context and instructions provided to the AI agent
- **tests**: Array of individual test cases
  - **prompt**: The coding task or question for the AI agent
  - **answers**: Array of strings that should appear in correct responses
  - **negative_answers**: Optional array of patterns that indicate incorrect responses

## Usage Examples

### Loading Test Suites

```typescript
import { TestSuiteReader, loadSuite, listSuites } from './test-suites';

// Create a reader instance
const reader = new TestSuiteReader();

// List all available test suites
const suiteIds = await reader.listSuites();
console.log('Available suites:', suiteIds);

// Load a specific test suite
const suite = await reader.loadSuite('react-basics');
console.log(`Loaded: ${suite.name} with ${suite.tests.length} tests`);

// Quick load using convenience function
const expressSuite = await loadSuite('express-api');
```

### Validation

```typescript
import { validateTestSuite } from './test-suites';

const validation = validateTestSuite(suiteData);
if (!validation.valid) {
  console.error('Validation errors:', validation.errors);
}
```

## Creating New Test Suites

### Best Practices

1. **Clear Prompts**: Write specific, actionable prompts that clearly describe the coding task
2. **Focused Answers**: Include key patterns, function names, and concepts that indicate correct implementation
3. **Negative Answers**: Add common mistakes or anti-patterns to catch incorrect responses
4. **System Prompts**: Provide clear context about the programming language, framework, and coding standards

### Example Test Suite

```json
{
  "name": "Python Functions",
  "description": "Tests basic Python function creation and usage",
  "system_prompt": "You are a Python developer. Write clean, readable Python code following PEP 8 standards. Use descriptive variable names and include proper docstrings.",
  "tests": [
    {
      "prompt": "Create a Python function called 'calculate_area' that takes radius as a parameter and returns the area of a circle.",
      "answers": [
        "def calculate_area",
        "radius",
        "return",
        "3.14159",
        "radius ** 2"
      ],
      "negative_answers": [
        "class",
        "global",
        "print("
      ]
    }
  ]
}
```

## File Organization

```
ts-bench/src/test-suites/
├── types.ts           # TypeScript interfaces
├── reader.ts          # TestSuiteReader class
├── utils.ts           # Utility functions
├── index.ts           # Main exports
├── README.md          # This documentation
└── samples/           # Sample test suites
    ├── react-basics.json
    └── express-api.json
```

## API Reference

### TestSuiteReader

Main class for loading and managing test suites.

```typescript
class TestSuiteReader {
  constructor(baseDir?: string)
  
  async listSuites(): Promise<string[]>
  async loadSuite(id: string, options?: LoadOptions): Promise<LoadedSuite>
  async suiteExists(id: string): Promise<boolean>
  validateSuite(suite: any): ValidationResult
}
```

### Utility Functions

```typescript
// Generate deterministic test IDs
computeTestSignature(test: TestCase, systemPrompt: string): string
signatureHash(signature: string): string

// Validation
validateTestSuite(suite: any): ValidationResult
validateTestCase(testCase: any): ValidationResult

// String utilities
safeFilename(str: string): string
normalizeAnswers(answers: string[]): string[]
```

## Integration with ts-bench

This test suite system is designed to integrate with the existing ts-bench infrastructure:

- Compatible with existing result types
- Follows ts-bench patterns for error handling and logging
- Can be used alongside Exercism exercises
- Supports the same agent factory system

## Future Enhancements

- **Metadata Support**: Add tags, difficulty levels, and categorization
- **Template System**: Support for parameterized test cases
- **Multi-language**: Extend beyond JavaScript/TypeScript
- **Scoring**: Advanced scoring algorithms for partial matches
- **IDE Integration**: VS Code extension for test suite authoring
